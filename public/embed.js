(function(){
  const STYLE_ID = 'ai-agent-portal-embed-style';
  const CONTAINER_ID = 'ai-agent-portal-container';
  const TOGGLE_ID = 'ai-agent-portal-toggle';
  const IFRAME_ID = 'ai-agent-portal-iframe';

  function ensureStyle() {
    if (document.getElementById(STYLE_ID)) return;
    const style = document.createElement('style');
    style.id = STYLE_ID;
    style.textContent = `
      .ai-agent-portal-hidden { display: none !important; }
      #${TOGGLE_ID} {
        position: fixed; right: 20px; bottom: 20px; width: 56px; height: 56px;
        border-radius: 50%; background: #1e80ff; color: #fff; border: none; cursor: pointer;
        box-shadow: 0 6px 20px rgba(0,0,0,0.2); z-index: 2147483000; display: flex; align-items: center; justify-content: center;
        font-size: 24px;
      }
      #${TOGGLE_ID}:hover { filter: brightness(1.05); }
      #${CONTAINER_ID} {
        position: fixed; top: 0; right: 0; height: 100vh; width: min(520px, 100vw);
        background: #fff; box-shadow: -8px 0 24px rgba(0,0,0,0.12); z-index: 2147482999;
        transform: translateX(100%); transition: transform 0.25s ease;
        display: flex; flex-direction: column; overflow: hidden; border-left: 1px solid #eaeaea;
        min-width: 320px; max-width: 90vw;
      }
      #${CONTAINER_ID}.open { transform: translateX(0); }
      #${CONTAINER_ID}.resizing { transition: none; }
      .ai-agent-resize-handle {
        position: absolute; left: 0; top: 0; width: 4px; height: 100%;
        background: transparent; cursor: ew-resize; z-index: 10;
      }
      .ai-agent-resize-handle:hover {
        background: #1e80ff; opacity: 0.5;
      }
      .ai-agent-resize-handle.dragging {
        background: #1e80ff; opacity: 0.8;
      }
      #${IFRAME_ID} { border: 0; width: 100%; height: 100%; }
      @media (max-width: 480px) {
        #${CONTAINER_ID} { width: 100vw; min-width: 100vw; }
        .ai-agent-resize-handle { display: none; }
      }
    `;
    document.head.appendChild(style);
  }

  function createToggle(iconHtml) {
    if (document.getElementById(TOGGLE_ID)) return document.getElementById(TOGGLE_ID);
    const btn = document.createElement('button');
    btn.id = TOGGLE_ID;
    btn.setAttribute('aria-label', 'Open AI Agent');
    btn.innerHTML = iconHtml || '🤖';
    document.body.appendChild(btn);

    // 添加拖拽功能
    makeDraggable(btn);

    return btn;
  }

  function makeDraggable(element) {
    let isDragging = false;
    let startX, startY, initialX, initialY;
    let hasMoved = false;
    let suppressNextClick = false;

    function onMouseDown(e) {
      isDragging = true;
      hasMoved = false;
      startX = e.clientX;
      startY = e.clientY;

      const rect = element.getBoundingClientRect();
      initialX = rect.left;
      initialY = rect.top;

      element.style.transition = 'none';
      element.style.cursor = 'grabbing';

      document.addEventListener('mousemove', onMouseMove);
      document.addEventListener('mouseup', onMouseUp);
      // 阻止选中文本，但不自行触发 click，交由原生 click 处理
      e.preventDefault();
    }

    function onMouseMove(e) {
      if (!isDragging) return;

      const deltaX = e.clientX - startX;
      const deltaY = e.clientY - startY;

      // 如果移动距离超过5px，认为是拖拽而非点击
      if (Math.abs(deltaX) > 5 || Math.abs(deltaY) > 5) {
        hasMoved = true;
      }

      let newX = initialX + deltaX;
      let newY = initialY + deltaY;

      // 边界限制：确保按钮不会完全移出视窗
      const btnSize = 56; // 按钮尺寸
      const margin = 10;
      newX = Math.max(margin, Math.min(window.innerWidth - btnSize - margin, newX));
      newY = Math.max(margin, Math.min(window.innerHeight - btnSize - margin, newY));

      element.style.left = newX + 'px';
      element.style.top = newY + 'px';
      element.style.right = 'auto';
      element.style.bottom = 'auto';
    }

    function onMouseUp() {
      isDragging = false;
      element.style.transition = '';
      element.style.cursor = 'pointer';

      document.removeEventListener('mousemove', onMouseMove);
      document.removeEventListener('mouseup', onMouseUp);

      // 如果发生过拖拽，则抑制紧随的 click 事件
      if (hasMoved) {
        suppressNextClick = true;
      }
    }

    // 捕获阶段拦截 click，防止拖拽后触发开合
    element.addEventListener('click', function(e){
      if (suppressNextClick) {
        e.preventDefault();
        e.stopImmediatePropagation();
        suppressNextClick = false;
      }
    }, true);

    element.addEventListener('mousedown', onMouseDown);
    element.style.cursor = 'pointer';
  }

  function makeResizable(container, resizeHandle) {
    let isResizing = false;
    let startX = 0;
    let startWidth = 0;
    const minWidth = 320;
    const maxWidth = window.innerWidth * 0.9;

    function onMouseDown(e) {
      isResizing = true;
      startX = e.clientX;
      startWidth = container.offsetWidth;

      container.classList.add('resizing');
      resizeHandle.classList.add('dragging');

      document.addEventListener('mousemove', onMouseMove);
      document.addEventListener('mouseup', onMouseUp);

      // 防止选中文本
      e.preventDefault();
      document.body.style.userSelect = 'none';
    }

    function onMouseMove(e) {
      if (!isResizing) return;

      const deltaX = startX - e.clientX; // 向左拖拽为正值
      let newWidth = startWidth + deltaX;

      // 限制宽度范围
      newWidth = Math.max(minWidth, Math.min(maxWidth, newWidth));

      container.style.width = newWidth + 'px';
    }

    function onMouseUp() {
      isResizing = false;
      container.classList.remove('resizing');
      resizeHandle.classList.remove('dragging');

      document.removeEventListener('mousemove', onMouseMove);
      document.removeEventListener('mouseup', onMouseUp);

      document.body.style.userSelect = '';

      // 保存宽度到localStorage
      try {
        localStorage.setItem('ai-agent-portal-width', container.style.width);
      } catch (e) {
        // 忽略localStorage错误
      }
    }

    // 恢复保存的宽度
    function restoreWidth() {
      try {
        const savedWidth = localStorage.getItem('ai-agent-portal-width');
        if (savedWidth) {
          const width = parseInt(savedWidth);
          if (width >= minWidth && width <= maxWidth) {
            container.style.width = savedWidth;
          }
        }
      } catch (e) {
        // 忽略localStorage错误
      }
    }

    resizeHandle.addEventListener('mousedown', onMouseDown);

    // 页面加载时恢复宽度
    restoreWidth();

    // 窗口大小改变时重新计算最大宽度
    window.addEventListener('resize', () => {
      const newMaxWidth = window.innerWidth * 0.9;
      if (container.offsetWidth > newMaxWidth) {
        container.style.width = newMaxWidth + 'px';
      }
    });
  }

  function createContainer() {
    if (document.getElementById(CONTAINER_ID)) return document.getElementById(CONTAINER_ID);
    const container = document.createElement('div');
    container.id = CONTAINER_ID;

    // 创建调节器
    const resizeHandle = document.createElement('div');
    resizeHandle.className = 'ai-agent-resize-handle';

    const iframe = document.createElement('iframe');
    iframe.id = IFRAME_ID;

    container.appendChild(resizeHandle);
    container.appendChild(iframe);
    document.body.appendChild(container);

    // 添加调节功能
    makeResizable(container, resizeHandle);

    return container;
  }

  //构建门户页面的URL
  async function buildPortalUrl(baseUrl, app, meta) {
    try {
      const url = new URL(baseUrl, window.location.origin);
      url.searchParams.set('app', app);

      // 将meta作为一个单一的参数放在URL中
      if (meta && typeof meta === 'object' && Object.keys(meta).length > 0) {
        // 先对meta中每个参数的值进行压缩编码处理
        const processedMeta = {};
        for (const key of Object.keys(meta)) {
          if (meta[key] !== undefined && meta[key] !== null) {
            const encodedValue = await compressAndEncodeBase64(String(meta[key]));
            processedMeta[key] = encodedValue;
          }
        }

        const metaString = JSON.stringify(processedMeta);
        // 对metaString进行加密
        const encryptedMeta = simpleEncrypt(metaString);
        url.searchParams.set('meta', encryptedMeta);
      }

      return url.toString();
    } catch (e) {
      return baseUrl;
    }
  }

  // 参数序列化
  async function compressAndEncodeBase64(input) {
      try {
        //使用TextEncoder需要Polyfill（如浏览器不支持需引入）
        const encoder = new TextEncoder();
        const uint8Array = encoder.encode(input);
        //创建Blob对象
        const blob = new Blob([uint8Array]);
        // 创建可压缩的响应流（需要浏览器支持CompressionStream）
        const compressedStream = blob.stream().pipeThrough(new CompressionStream("gzip"));
        // 将压缩流转换为Response对象
        const response = new Response(compressedStream);
        // 读取ArrayBuffer数据
        const arrayBuffer = await response.arrayBuffer();
        const compressedUint8Array = new Uint8Array(arrayBuffer);
        const charString = String.fromCharCode(...compressedUint8Array);
        return btoa(charString);
      } catch (e) {
          throw new Error(`压缩或编码失败: ${e.message}`);
      }
  }

  // 简单加密函数 - 使用XOR和Base64
  function simpleEncrypt(text, key = 'AIAgentPortal2025') {
    try {
      const keyBytes = new TextEncoder().encode(key);
      const textBytes = new TextEncoder().encode(text);
      const encrypted = new Uint8Array(textBytes.length);

      for (let i = 0; i < textBytes.length; i++) {
        encrypted[i] = textBytes[i] ^ keyBytes[i % keyBytes.length];
      }

      // 转换为Base64并进行URL安全编码
      const base64 = btoa(String.fromCharCode(...encrypted));
      return base64.replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
    } catch (e) {
      console.error('加密失败:', e);
      return text; // 失败时返回原文
    }
  }

  async function init(options) {
    ensureStyle();
    const {
      //AI门户地址
      portalBaseUrl = 'http://localhost:5173/',
      //应用ID
      app = '',
      //应用参数
      meta = {},
      iconHtml = '🤖',
      autoOpen = false,
      onOpen,
      onClose
    } = options || {};

    const toggle = createToggle(iconHtml);
    const container = createContainer();
    const iframe = container.querySelector('#' + IFRAME_ID);

    // 直接将meta作为参数传递给buildPortalUrl
    const url = await buildPortalUrl(portalBaseUrl, app, meta);
    iframe.src = url;

    function open() {
      container.classList.add('open');
      if (typeof onOpen === 'function') onOpen();
    }
    function close() {
      container.classList.remove('open');
      if (typeof onClose === 'function') onClose();
    }
    function toggleOpen() {
      if (container.classList.contains('open')) close(); else open();
    }

    // 仅在未被拖拽抑制时响应点击，抑制逻辑在 makeDraggable 内处理
    toggle.addEventListener('click', toggleOpen);

    if (autoOpen) open();

    return { open, close, toggle: toggleOpen, iframe };
  }

  window.AIPortal = { init };
})();

