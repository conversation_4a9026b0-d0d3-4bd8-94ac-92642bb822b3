<template>
  <div class="app-container">
    <iframe
      class="dify-frame"
      :src="resolvedUrl"
      frameborder="0"
      allow="clipboard-write; microphone; camera;"
    ></iframe>
  </div>
</template>

<script setup>
import { computed } from 'vue'

// 简单解密函数 - 对应embed.js中的加密
function simpleDecrypt(encryptedText) {
  try {
    // 恢复Base64格式
    let base64 = encryptedText.replace(/-/g, '+').replace(/_/g, '/');
    // 补齐Base64填充
    while (base64.length % 4) {
      base64 += '=';
    }

    const keyBytes = new TextEncoder().encode(import.meta.env.VITE_XOR_KEY);
    const encryptedBytes = new Uint8Array(atob(base64).split('').map(c => c.charCodeAt(0)));
    const decrypted = new Uint8Array(encryptedBytes.length);

    for (let i = 0; i < encryptedBytes.length; i++) {
      decrypted[i] = encryptedBytes[i] ^ keyBytes[i % keyBytes.length];
    }

    return new TextDecoder().decode(decrypted);
  } catch (e) {
    console.error('解密失败:', e);
    return encryptedText; // 失败时返回原文
  }
}

// 优先读取 URL query: ?difyUrl=...，否则读取环境变量 VITE_DIFY_URL
const resolvedUrl = computed(() => {
  const url = new URL(window.location.href)
  const app = url.searchParams.get("app");
  //dify应用地址
  const difyUrl = new URL(import.meta.env.VITE_DIFY_URL + app);

  // 提取URL中的meta参数
  const metaParam = url.searchParams.get("meta");
  if (metaParam) {
    try {
      // 先解密meta参数
      const decryptedMeta = simpleDecrypt(metaParam);
      const meta = JSON.parse(decryptedMeta);
      // 将meta中的每个参数作为独立参数放在difyUrl中
      if (meta && typeof meta === 'object') {
        Object.keys(meta).forEach(key => {
          if (meta[key] !== undefined && meta[key] !== null) {
            difyUrl.searchParams.set(key, meta[key]);
          }
        });
      }
    } catch (e) {
      console.error('解析meta参数失败:', e);
    }
  }
  return difyUrl;
})
</script>

<style scoped>
.app-container {
  position: fixed;
  inset: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: stretch;
  justify-content: stretch;
  overflow: hidden;
}

.dify-frame {
  border: none;
  width: 100%;
  height: 100%;
}
</style>

