<template>
  <div class="app-container">
    <iframe
      class="dify-frame"
      :src="resolvedUrl"
      frameborder="0"
      allow="clipboard-write; microphone; camera;"
    ></iframe>
  </div>
</template>

<script setup>
import { computed } from 'vue'

function getQueryParam(name) {
  const url = new URL(window.location.href)
  return url.searchParams.get(name)
}

// 优先读取 URL query: ?difyUrl=...，否则读取环境变量 VITE_DIFY_URL
const resolvedUrl = computed(() => {
  const url = new URL(window.location.href)
  const app = url.searchParams.get("app");
  //dify应用地址
  const difyUrl = new URL(import.meta.env.VITE_DIFY_URL + app);
  // 把url.searchParams中除app外的所有参数拼接到difyUrl上
  url.searchParams.forEach((value, key) => {
    if (key !== 'app') {
      difyUrl.searchParams.set(key, value);
    }
  });
  debugger;
  return difyUrl;
})
</script>

<style scoped>
.app-container {
  position: fixed;
  inset: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: stretch;
  justify-content: stretch;
  overflow: hidden;
}

.dify-frame {
  border: none;
  width: 100%;
  height: 100%;
}
</style>

