<template>
  <div class="app-container">
    <iframe
      class="dify-frame"
      :src="resolvedUrl"
      frameborder="0"
      allow="clipboard-write; microphone; camera;"
    ></iframe>
  </div>
</template>

<script setup>
import { computed } from 'vue'

function getQueryParam(name) {
  const url = new URL(window.location.href)
  return url.searchParams.get(name)
}

// 优先读取 URL query: ?difyUrl=...，否则读取环境变量 VITE_DIFY_URL
const resolvedUrl = computed(() => {
  const url = new URL(window.location.href)
  const app = url.searchParams.get("app");
  //dify应用地址
  const difyUrl = new URL(import.meta.env.VITE_DIFY_URL + app);

  // 提取URL中的meta参数
  const metaParam = url.searchParams.get("meta");
  if (metaParam) {
    try {
      const meta = JSON.parse(metaParam);
      // 将meta中的每个参数作为独立参数放在difyUrl中
      if (meta && typeof meta === 'object') {
        Object.keys(meta).forEach(key => {
          if (meta[key] !== undefined && meta[key] !== null) {
            difyUrl.searchParams.set(key, meta[key]);
          }
        });
      }
    } catch (e) {
      console.error('解析meta参数失败:', e);
    }
  }

  // 把url.searchParams中除app和meta外的所有参数拼接到difyUrl上
  // url.searchParams.forEach((value, key) => {
  //   if (key !== 'app' && key !== 'meta') {
  //     difyUrl.searchParams.set(key, value);
  //   }
  // });
  debugger;
  return difyUrl;
})
</script>

<style scoped>
.app-container {
  position: fixed;
  inset: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: stretch;
  justify-content: stretch;
  overflow: hidden;
}

.dify-frame {
  border: none;
  width: 100%;
  height: 100%;
}
</style>

