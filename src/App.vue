<template>
  <div class="app-container">
    <iframe
      class="dify-frame"
      :src="resolvedUrl"
      frameborder="0"
      allow="clipboard-write; microphone; camera;"
    ></iframe>
  </div>
</template>

<script setup>
import { computed } from 'vue'

function getQueryParam(name) {
  const url = new URL(window.location.href)
  return url.searchParams.get(name)
}

// 优先读取 URL query: ?difyUrl=...，否则读取环境变量 VITE_DIFY_URL
const resolvedUrl = computed(() => {
  const url = new URL(window.location.href)
  const app = url.searchParams.get("app");
  // const difyUrl = "http://************:9009/chatbot/" + app;
  const difyUrl = new URL(window.location.href, "http://************:9009/chatbot/" + app);
  url.searchParams.entries
  const q = getQueryParam('difyUrl')
  return q || import.meta.env.VITE_DIFY_URL || ''
})
</script>

<style scoped>
.app-container {
  position: fixed;
  inset: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: stretch;
  justify-content: stretch;
  overflow: hidden;
}

.dify-frame {
  border: none;
  width: 100%;
  height: 100%;
}
</style>

